# Database Configuration
MONGODB_URI=mongodb+srv://connect:<EMAIL>/?retryWrites=true&w=majority&appName=pedi
MONGODB_DB_NAME=pedi

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:5000

# M-Pesa Daraja API Configuration
MPESA_CONSUMER_KEY=o0CgPajrGXpVcCUIeKBfgLlvox3bHGyRXd0LMEF4guXTTPwr
MPESA_CONSUMER_SECRET=VkbnCj7GB2368hVA7WpQAqcHDXKr1QPk01v9heGDtcxU2PdLio10cjjnxAx8198k
MPESA_ENVIRONMENT=sandbox
MPESA_PASSKEY=your-mpesa-passkey
MPESA_SHORTCODE=N/A
MPESA_CALLBACK_URL=https://yourdomain.com/api/mpesa/callback

# Africa's Talking API Configuration
AFRICAS_TALKING_USERNAME=airtimeapi
AFRICAS_TALKING_API_KEY=atsk_c6ce425cce945764c9149e310bd6773c210abc89d095edcc3b8ce9f7140f3b9ea0c524c6
AFRICAS_TALKING_SENDER_ID=AFTKNG

# Google Gemini API Configuration
GOOGLE_GEMINI_API_KEY=AIzaSyA9f_zDsYjVWQx2DWO_1zuWlVofQi48UMg

# File Upload Configuration
CLOUDINARY_CLOUD_NAME=connect
CLOUDINARY_API_KEY=862788588369259
CLOUDINARY_API_SECRET=t38zX9oKG2Xi_piRqxg82mDKLS8

# Email Configuration (Optional - for admin notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Redis Configuration (for sessions and caching)
REDIS_URL=redis://localhost:6379

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=your-session-secret-key

# Feature Flags
ENABLE_AI_ASSISTANT=true
ENABLE_MPESA_PAYMENTS=true
ENABLE_SMS_NOTIFICATIONS=true
ENABLE_CHAT_SYSTEM=true

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Development
DEBUG=pedi:*
