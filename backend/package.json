{"name": "pedi-backend", "version": "1.0.0", "description": "Backend API for Pedi - Sustainable clothing exchange platform", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "compression": "^1.7.4", "morgan": "^1.10.0", "socket.io": "^4.7.4", "node-cron": "^3.0.3", "axios": "^1.6.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "prettier": "^3.1.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["express", "typescript", "mongodb", "api", "sustainable-fashion", "clothing-exchange"], "author": "Pedi Team", "license": "MIT"}