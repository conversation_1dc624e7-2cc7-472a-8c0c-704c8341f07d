import { Request, Response, NextFunction } from 'express';
import { tokenService } from '../services/token';
import User from '../models/User';
import ClothingItem from '../models/ClothingItem';
import logger from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    phoneNumber: string;
    role: string;
  };
}

/**
 * Middleware to track user activity and award tokens for various actions
 */
export const trackUserActivity = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user?.id) {
      return next();
    }

    const userId = req.user.id;
    const method = req.method;
    const path = req.path;

    // Track different activities based on the route and method
    let activityType: string | null = null;
    let metadata: any = {};

    // Profile completion tracking
    if (method === 'PUT' && path === '/profile') {
      activityType = 'profile_update';
      metadata = { section: 'profile' };
    }

    // Review submission tracking
    if (method === 'POST' && path.includes('/reviews')) {
      activityType = 'review_given';
      metadata = { 
        reviewType: req.body?.rating ? 'rating' : 'text',
        rating: req.body?.rating 
      };
    }

    // Donation tracking
    if (method === 'POST' && path.includes('/donate')) {
      activityType = 'donation';
      metadata = { 
        itemId: req.body?.itemId,
        charityId: req.body?.charityId 
      };
    }

    // Swap completion tracking
    if (method === 'POST' && path.includes('/swap') && path.includes('/complete')) {
      activityType = 'swap_completed';
      metadata = { 
        swapId: req.params?.id,
        partnerId: req.body?.partnerId 
      };
    }

    // Referral tracking
    if (method === 'POST' && path.includes('/referral')) {
      activityType = 'referral';
      metadata = { 
        referredPhone: req.body?.phoneNumber,
        referralCode: req.body?.code 
      };
    }

    // Store activity info for post-response processing
    if (activityType) {
      res.locals.tokenActivity = {
        userId,
        activityType,
        metadata
      };
    }

    next();
  } catch (error) {
    logger.error('Error in trackUserActivity middleware:', error);
    next(); // Don't block the request if tracking fails
  }
};

/**
 * Middleware to process token rewards after successful response
 */
export const processTokenRewards = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const originalSend = res.send;

  res.send = function(data: any) {
    // Call original send first
    const result = originalSend.call(this, data);

    // Process token rewards asynchronously after response is sent
    setImmediate(async () => {
      try {
        const activity = res.locals.tokenActivity;
        if (!activity) return;

        const { userId, activityType, metadata } = activity;

        // Only award tokens if the response was successful
        const responseData = typeof data === 'string' ? JSON.parse(data) : data;
        if (!responseData?.success) return;

        // Award tokens based on activity type
        await awardActivityTokens(userId, activityType, metadata, responseData);

      } catch (error) {
        logger.error('Error processing token rewards:', error);
      }
    });

    return result;
  };

  next();
};

/**
 * Award tokens for specific activities
 */
async function awardActivityTokens(
  userId: string, 
  activityType: string, 
  metadata: any, 
  responseData: any
) {
  try {
    let description = '';
    let referenceId = '';
    let enhancedMetadata = { ...metadata };

    switch (activityType) {
      case 'profile_update':
        // Check if profile is now complete
        const user = await User.findById(userId);
        if (user && isProfileComplete(user)) {
          description = 'Completed your profile';
          enhancedMetadata.isFirstTime = !user.profileCompleted;
          enhancedMetadata.completionPercentage = 100;
          
          // Mark profile as completed
          if (!user.profileCompleted) {
            user.profileCompleted = true;
            await user.save();
          }
        } else {
          return; // Don't award tokens for partial updates
        }
        break;

      case 'review_given':
        description = `Wrote a helpful review`;
        referenceId = responseData.data?.id || '';
        enhancedMetadata.isFirstTime = await isFirstTimeAction(userId, 'review');
        break;

      case 'donation':
        description = `Donated clothing item to charity`;
        referenceId = metadata.itemId || '';
        enhancedMetadata.isFirstTime = await isFirstTimeAction(userId, 'donation');
        enhancedMetadata.sustainabilityImpact = 'high';
        break;

      case 'swap_completed':
        description = `Completed a clothing swap`;
        referenceId = metadata.swapId || '';
        enhancedMetadata.isFirstTime = await isFirstTimeAction(userId, 'swap');
        enhancedMetadata.sustainabilityImpact = 'medium';
        break;

      case 'referral':
        description = `Referred a new user to Pedi`;
        enhancedMetadata.isFirstTime = false; // Referrals are always rewarded
        enhancedMetadata.socialImpact = 'high';
        break;

      default:
        return; // Unknown activity type
    }

    // Award the tokens
    const result = await tokenService.earnTokens({
      userId,
      action: activityType,
      referenceId,
      description,
      metadata: enhancedMetadata
    });

    if (result.success) {
      logger.info('Tokens awarded for activity', {
        userId,
        activityType,
        tokensEarned: result.tokensEarned,
        newBalance: result.newBalance
      });
    }

  } catch (error) {
    logger.error('Error awarding activity tokens:', error);
  }
}

/**
 * Check if user profile is complete
 */
function isProfileComplete(user: any): boolean {
  const requiredFields = [
    'firstName',
    'lastName',
    'phoneNumber',
    'location.county',
    'location.town'
  ];

  return requiredFields.every(field => {
    const value = field.split('.').reduce((obj, key) => obj?.[key], user);
    return value && value.toString().trim().length > 0;
  });
}

/**
 * Check if this is the first time user performs this action
 */
async function isFirstTimeAction(userId: string, action: string): Promise<boolean> {
  try {
    const user = await User.findById(userId);
    if (!user) return false;

    switch (action) {
      case 'review':
        return user.totalReviews === 0;
      case 'donation':
        return user.totalDonations === 0;
      case 'swap':
        return user.totalSwaps === 0;
      default:
        return false;
    }
  } catch (error) {
    logger.error('Error checking first time action:', error);
    return false;
  }
}

/**
 * Middleware specifically for listing rewards with quality bonuses
 */
export const processListingRewards = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const originalSend = res.send;

  res.send = function(data: any) {
    const result = originalSend.call(this, data);

    setImmediate(async () => {
      try {
        if (!req.user?.id) return;

        const responseData = typeof data === 'string' ? JSON.parse(data) : data;
        if (!responseData?.success || !responseData?.data) return;

        const userId = req.user.id;
        const clothingItem = responseData.data;

        // Check if this is user's first listing
        const user = await User.findById(userId);
        const isFirstListing = user ? user.totalListings === 0 : false;

        // Calculate quality bonus
        let qualityBonus = 0;
        if (clothingItem.condition === 'excellent') qualityBonus += 5;
        if (clothingItem.sustainabilityInfo?.score > 80) qualityBonus += 3;

        const enhancedMetadata = {
          condition: clothingItem.condition,
          category: clothingItem.category,
          sustainabilityScore: clothingItem.sustainabilityInfo?.score || 0,
          isFirstTime: isFirstListing,
          qualityBonus,
          brand: clothingItem.brand
        };

        // Award tokens with enhanced metadata
        const tokenResult = await tokenService.earnTokens({
          userId,
          action: isFirstListing ? 'first_listing' : 'listing',
          referenceId: clothingItem._id || clothingItem.id,
          description: `Listed ${clothingItem.title}`,
          metadata: enhancedMetadata
        });

        logger.info('Listing tokens awarded', {
          userId,
          itemId: clothingItem._id || clothingItem.id,
          tokensEarned: tokenResult.tokensEarned,
          qualityBonus,
          isFirstListing
        });

      } catch (error) {
        logger.error('Error processing listing rewards:', error);
      }
    });

    return result;
  };

  next();
};
