import mongoose, { Document, Schema } from 'mongoose';

export interface ICharityPartner extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  category: string;
  contactInfo: {
    email: string;
    phone: string;
    website?: string;
    address: {
      county: string;
      town: string;
      specificLocation: string;
    };
  };
  logo?: string;
  isActive: boolean;
  acceptedItemTypes: string[];
  requirements: {
    condition: string[];
    categories: string[];
    notes?: string;
  };
  stats: {
    totalDonationsReceived: number;
    totalItemsReceived: number;
    totalBeneficiaries: number;
  };
  verificationStatus: 'pending' | 'verified' | 'suspended';
  verifiedBy?: mongoose.Types.ObjectId;
  verificationDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const charityPartnerSchema = new Schema<ICharityPartner>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500,
  },
  category: {
    type: String,
    required: true,
    enum: [
      'Children & Youth',
      'Women Empowerment',
      'Education',
      'Healthcare',
      'Environment',
      'Community Development',
      'Disaster Relief',
      'Elderly Care',
      'Disability Support',
      'General Welfare'
    ],
  },
  contactInfo: {
    email: {
      type: String,
      required: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
    },
    phone: {
      type: String,
      required: true,
      match: [/^(\+254|0)[17]\d{8}$/, 'Please enter a valid Kenyan phone number'],
    },
    website: {
      type: String,
      trim: true,
    },
    address: {
      county: {
        type: String,
        required: true,
      },
      town: {
        type: String,
        required: true,
      },
      specificLocation: {
        type: String,
        required: true,
      },
    },
  },
  logo: {
    type: String,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  acceptedItemTypes: [{
    type: String,
    enum: [
      'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
      'Formal', 'Casual', 'Sportswear', 'Traditional', 'Children Clothing',
      'Baby Items', 'School Uniforms'
    ],
  }],
  requirements: {
    condition: [{
      type: String,
      enum: ['New', 'Like New', 'Good', 'Fair'],
    }],
    categories: [{
      type: String,
      enum: [
        'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
        'Formal', 'Casual', 'Sportswear', 'Traditional', 'Children Clothing'
      ],
    }],
    notes: {
      type: String,
      trim: true,
    },
  },
  stats: {
    totalDonationsReceived: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalItemsReceived: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalBeneficiaries: {
      type: Number,
      default: 0,
      min: 0,
    },
  },
  verificationStatus: {
    type: String,
    enum: ['pending', 'verified', 'suspended'],
    default: 'pending',
  },
  verifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
  verificationDate: {
    type: Date,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
charityPartnerSchema.index({ name: 1 });
charityPartnerSchema.index({ category: 1 });
charityPartnerSchema.index({ isActive: 1 });
charityPartnerSchema.index({ verificationStatus: 1 });
charityPartnerSchema.index({ 'contactInfo.address.county': 1 });

export default mongoose.model<ICharityPartner>('CharityPartner', charityPartnerSchema);
