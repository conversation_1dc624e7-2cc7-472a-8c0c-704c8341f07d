import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  phoneNumber: string;
  email?: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
  location: {
    county: string;
    town: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  isVerified: boolean;
  isActive: boolean;
  role: 'user' | 'admin' | 'moderator';
  pediTokens: number;
  rating: {
    average: number;
    count: number;
  };
  preferences: {
    sizes: string[];
    categories: string[];
    brands: string[];
    priceRange: {
      min: number;
      max: number;
    };
  };
  sustainabilityScore: number;
  totalDonations: number;
  totalSwaps: number;
  joinedAt: Date;
  lastActive: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const userSchema = new Schema<IUser>({
  phoneNumber: {
    type: String,
    required: true,
    unique: true,
    match: [/^(\+254|0)[17]\d{8}$/, 'Please enter a valid Kenyan phone number'],
  },
  email: {
    type: String,
    unique: true,
    sparse: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  profilePicture: {
    type: String,
    default: null,
  },
  location: {
    county: {
      type: String,
      required: true,
      enum: [
        'Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Thika', 'Malindi',
        'Kitale', 'Garissa', 'Kakamega', 'Machakos', 'Meru', 'Nyeri', 'Kericho',
        'Embu', 'Migori', 'Homa Bay', 'Naivasha', 'Voi', 'Kilifi', 'Lamu',
        'Marsabit', 'Wajir', 'Mandera', 'Isiolo', 'Turkana', 'West Pokot',
        'Samburu', 'Trans Nzoia', 'Uasin Gishu', 'Elgeyo Marakwet', 'Nandi',
        'Baringo', 'Laikipia', 'Nyandarua', 'Kirinyaga', 'Muranga',
        'Kiambu', 'Kajiado', 'Makueni', 'Kitui', 'Tana River', 'Taita Taveta',
        'Kwale'
      ],
    },
    town: {
      type: String,
      required: true,
      trim: true,
    },
    coordinates: {
      latitude: { type: Number },
      longitude: { type: Number },
    },
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  role: {
    type: String,
    enum: ['user', 'admin', 'moderator'],
    default: 'user',
  },
  pediTokens: {
    type: Number,
    default: 100, // Welcome bonus
    min: 0,
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5,
    },
    count: {
      type: Number,
      default: 0,
      min: 0,
    },
  },
  preferences: {
    sizes: [{
      type: String,
      enum: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
    }],
    categories: [{
      type: String,
      enum: [
        'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
        'Formal', 'Casual', 'Sportswear', 'Traditional', 'Vintage'
      ],
    }],
    brands: [String],
    priceRange: {
      min: { type: Number, default: 0 },
      max: { type: Number, default: 10000 },
    },
  },
  sustainabilityScore: {
    type: Number,
    default: 0,
    min: 0,
    max: 1000,
  },
  totalDonations: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalSwaps: {
    type: Number,
    default: 0,
    min: 0,
  },
  joinedAt: {
    type: Date,
    default: Date.now,
  },
  lastActive: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
userSchema.index({ phoneNumber: 1 });
userSchema.index({ email: 1 });
userSchema.index({ 'location.county': 1, 'location.town': 1 });
userSchema.index({ sustainabilityScore: -1 });
userSchema.index({ 'rating.average': -1 });

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Method to compare password (for future password implementation)
userSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  // For now, return true as we're using SMS OTP
  // This will be implemented when we add password functionality
  return true;
};

// Pre-save middleware to update lastActive
userSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.lastActive = new Date();
  }
  next();
});

export default mongoose.model<IUser>('User', userSchema);
