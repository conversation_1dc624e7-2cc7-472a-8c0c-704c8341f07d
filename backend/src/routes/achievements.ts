import express, { Request, Response, NextFunction } from 'express';
import { validationResult, query, param } from 'express-validator';
import { authenticateToken } from '../middleware/auth';
import { createError } from '../utils/errorHandler';
import { achievementService } from '../services/achievement';
import Achievement from '../models/Achievement';
import UserAchievement from '../models/UserAchievement';
import logger from '../utils/logger';

const router = express.Router();

// GET /api/achievements - Get all available achievements
router.get('/', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const achievements = await Achievement.find({ isActive: true })
      .sort({ category: 1, type: 1 });

    res.json({
      success: true,
      message: 'Achievements retrieved successfully',
      data: {
        achievements: achievements.map(achievement => ({
          id: achievement._id,
          name: achievement.name,
          description: achievement.description,
          icon: achievement.icon,
          category: achievement.category,
          type: achievement.type,
          requirements: achievement.requirements,
          rewards: achievement.rewards
        }))
      }
    });
  } catch (error) {
    logger.error('Error fetching achievements:', error);
    next(createError('Failed to fetch achievements', 500));
  }
});

// GET /api/achievements/user - Get user's achievements and progress
router.get('/user', authenticateToken, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = (req as any).user.id;
    
    const userAchievements = await achievementService.getUserAchievements(userId);
    
    // Separate completed and in-progress achievements
    const completed = userAchievements.filter(ua => ua.isCompleted);
    const inProgress = userAchievements.filter(ua => !ua.isCompleted);
    
    // Calculate statistics
    const stats = {
      totalCompleted: completed.length,
      totalTokensEarned: completed.reduce((sum, ua) => sum + ua.tokensAwarded, 0),
      totalAvailable: await Achievement.countDocuments({ isActive: true }),
      completionPercentage: Math.round((completed.length / await Achievement.countDocuments({ isActive: true })) * 100)
    };

    res.json({
      success: true,
      message: 'User achievements retrieved successfully',
      data: {
        completed,
        inProgress,
        stats
      }
    });
  } catch (error) {
    logger.error('Error fetching user achievements:', error);
    next(createError('Failed to fetch user achievements', 500));
  }
});

// POST /api/achievements/check - Check and award new achievements for user
router.post('/check', authenticateToken, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = (req as any).user.id;
    
    const newAchievements = await achievementService.checkUserAchievements(userId);
    
    res.json({
      success: true,
      message: newAchievements.length > 0 
        ? `Congratulations! You unlocked ${newAchievements.length} new achievement${newAchievements.length > 1 ? 's' : ''}!`
        : 'No new achievements unlocked',
      data: {
        newAchievements: newAchievements.map(ua => ({
          id: ua._id,
          achievement: ua.achievement,
          unlockedAt: ua.unlockedAt,
          tokensAwarded: ua.tokensAwarded
        })),
        count: newAchievements.length
      }
    });
  } catch (error) {
    logger.error('Error checking user achievements:', error);
    next(createError('Failed to check achievements', 500));
  }
});

// GET /api/achievements/leaderboard - Get achievement leaderboard
router.get('/leaderboard', 
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400, errors.array()));
      }

      const limit = parseInt(req.query.limit as string) || 10;
      
      const leaderboard = await achievementService.getAchievementLeaderboard(limit);
      
      res.json({
        success: true,
        message: 'Achievement leaderboard retrieved successfully',
        data: {
          leaderboard,
          count: leaderboard.length
        }
      });
    } catch (error) {
      logger.error('Error fetching achievement leaderboard:', error);
      next(createError('Failed to fetch achievement leaderboard', 500));
    }
  }
);

// GET /api/achievements/categories - Get achievements grouped by category
router.get('/categories', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const achievements = await Achievement.find({ isActive: true })
      .sort({ category: 1, type: 1 });

    const categories = achievements.reduce((acc, achievement) => {
      if (!acc[achievement.category]) {
        acc[achievement.category] = [];
      }
      acc[achievement.category].push({
        id: achievement._id,
        name: achievement.name,
        description: achievement.description,
        icon: achievement.icon,
        type: achievement.type,
        requirements: achievement.requirements,
        rewards: achievement.rewards
      });
      return acc;
    }, {} as Record<string, any[]>);

    res.json({
      success: true,
      message: 'Achievement categories retrieved successfully',
      data: {
        categories,
        categoryNames: Object.keys(categories)
      }
    });
  } catch (error) {
    logger.error('Error fetching achievement categories:', error);
    next(createError('Failed to fetch achievement categories', 500));
  }
});

// GET /api/achievements/:id - Get specific achievement details
router.get('/:id', 
  param('id').isMongoId().withMessage('Invalid achievement ID'),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400, errors.array()));
      }

      const achievementId = req.params.id;
      
      const achievement = await Achievement.findById(achievementId);
      
      if (!achievement) {
        return next(createError('Achievement not found', 404));
      }

      // Get completion statistics
      const totalUsers = await UserAchievement.distinct('user').countDocuments();
      const completedUsers = await UserAchievement.countDocuments({
        achievement: achievementId,
        isCompleted: true
      });

      const completionRate = totalUsers > 0 ? Math.round((completedUsers / totalUsers) * 100) : 0;

      res.json({
        success: true,
        message: 'Achievement details retrieved successfully',
        data: {
          achievement: {
            id: achievement._id,
            name: achievement.name,
            description: achievement.description,
            icon: achievement.icon,
            category: achievement.category,
            type: achievement.type,
            requirements: achievement.requirements,
            rewards: achievement.rewards
          },
          statistics: {
            totalUsers,
            completedUsers,
            completionRate
          }
        }
      });
    } catch (error) {
      logger.error('Error fetching achievement details:', error);
      next(createError('Failed to fetch achievement details', 500));
    }
  }
);

// POST /api/achievements/initialize - Initialize default achievements (admin only)
router.post('/initialize', authenticateToken, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userRole = (req as any).user.role;
    
    if (userRole !== 'admin') {
      return next(createError('Access denied. Admin role required.', 403));
    }

    await achievementService.initializeDefaultAchievements();
    
    res.json({
      success: true,
      message: 'Default achievements initialized successfully'
    });
  } catch (error) {
    logger.error('Error initializing achievements:', error);
    next(createError('Failed to initialize achievements', 500));
  }
});

export default router;
