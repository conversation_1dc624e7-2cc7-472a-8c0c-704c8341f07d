import axios from 'axios';
import { logger } from '@/utils/logger';

export interface AIConfig {
  apiKey: string;
  model: string;
}

export interface FashionAdviceRequest {
  userProfile?: {
    preferences: string[];
    size: string;
    style: string;
  };
  query: string;
  context?: {
    availableItems?: any[];
    userLocation?: string;
    season?: string;
  };
}

export interface FashionAdviceResponse {
  success: boolean;
  advice?: string;
  recommendations?: {
    items?: any[];
    tips?: string[];
    sustainabilityScore?: number;
  };
  error?: string;
}

export class AIService {
  private config: AIConfig;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta';

  constructor() {
    this.config = {
      apiKey: process.env.GOOGLE_GEMINI_API_KEY || '',
      model: 'gemini-pro',
    };
  }

  async getFashionAdvice(request: FashionAdviceRequest): Promise<FashionAdviceResponse> {
    try {
      if (!this.config.apiKey) {
        logger.warn('Google Gemini API key not configured, AI advice not available');
        return { 
          success: false, 
          error: 'AI service not configured',
          advice: 'AI fashion assistant is currently unavailable. Please try again later.'
        };
      }

      // Construct the prompt for fashion advice
      const prompt = this.buildFashionPrompt(request);

      const response = await axios.post(
        `${this.baseUrl}/models/${this.config.model}:generateContent?key=${this.config.apiKey}`,
        {
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            }
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const generatedText = response.data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!generatedText) {
        throw new Error('No response generated from AI service');
      }

      logger.info('AI fashion advice generated successfully');

      return {
        success: true,
        advice: generatedText,
        recommendations: this.parseRecommendations(generatedText),
      };

    } catch (error) {
      logger.error('Failed to get AI fashion advice:', error);
      return {
        success: false,
        error: 'Failed to generate fashion advice',
        advice: 'I\'m having trouble providing advice right now. Please try asking again in a moment!'
      };
    }
  }

  private buildFashionPrompt(request: FashionAdviceRequest): string {
    let prompt = `You are Pedi, an AI fashion assistant for a sustainable clothing exchange platform in Kenya. 
Your role is to provide helpful, culturally appropriate fashion advice while promoting sustainable fashion practices.

User Query: ${request.query}

`;

    if (request.userProfile) {
      prompt += `User Profile:
- Preferred styles: ${request.userProfile.preferences.join(', ')}
- Size: ${request.userProfile.size}
- Style preference: ${request.userProfile.style}

`;
    }

    if (request.context) {
      if (request.context.userLocation) {
        prompt += `User Location: ${request.context.userLocation}\n`;
      }
      if (request.context.season) {
        prompt += `Current Season: ${request.context.season}\n`;
      }
      if (request.context.availableItems && request.context.availableItems.length > 0) {
        prompt += `Available items on the platform: ${request.context.availableItems.length} items\n`;
      }
    }

    prompt += `
Guidelines for your response:
1. Be friendly, helpful, and encouraging
2. Promote sustainable fashion practices
3. Consider Kenya's climate and cultural context
4. Suggest ways to extend clothing lifespan
5. Encourage clothing swaps and donations
6. Keep responses concise but informative
7. Use emojis appropriately to make responses engaging
8. If suggesting specific items, focus on versatile pieces

Please provide practical fashion advice that aligns with sustainable practices and the Pedi platform's mission of circular fashion.`;

    return prompt;
  }

  private parseRecommendations(advice: string): any {
    // Simple parsing logic - in a real implementation, this could be more sophisticated
    const tips = [];
    const lines = advice.split('\n');
    
    for (const line of lines) {
      if (line.includes('tip:') || line.includes('suggestion:') || line.includes('recommend:')) {
        tips.push(line.trim());
      }
    }

    return {
      tips: tips.length > 0 ? tips : undefined,
      sustainabilityScore: this.calculateSustainabilityScore(advice),
    };
  }

  private calculateSustainabilityScore(advice: string): number {
    // Simple scoring based on sustainability keywords
    const sustainabilityKeywords = [
      'sustainable', 'eco-friendly', 'recycle', 'reuse', 'swap', 'donate',
      'circular', 'environment', 'waste', 'quality', 'durable', 'timeless'
    ];

    let score = 0;
    const lowerAdvice = advice.toLowerCase();
    
    for (const keyword of sustainabilityKeywords) {
      if (lowerAdvice.includes(keyword)) {
        score += 10;
      }
    }

    return Math.min(score, 100); // Cap at 100
  }

  async generateItemDescription(itemDetails: any): Promise<string> {
    try {
      const prompt = `Generate a compelling, sustainable-focused description for this clothing item:
      
Item: ${itemDetails.title}
Category: ${itemDetails.category}
Brand: ${itemDetails.brand || 'Unbranded'}
Condition: ${itemDetails.condition}
Size: ${itemDetails.size}
Color: ${itemDetails.color}

Create a description that:
1. Highlights the item's best features
2. Emphasizes its sustainability value
3. Mentions its potential for extending fashion lifecycle
4. Is engaging and honest about condition
5. Encourages sustainable fashion choices
6. Keeps it under 200 words

Write in a friendly, encouraging tone that promotes circular fashion.`;

      const response = await this.getFashionAdvice({
        query: prompt,
      });

      return response.advice || 'A great addition to any sustainable wardrobe!';

    } catch (error) {
      logger.error('Failed to generate item description:', error);
      return 'A wonderful piece ready for its next fashion journey!';
    }
  }
}

export const aiService = new AIService();
