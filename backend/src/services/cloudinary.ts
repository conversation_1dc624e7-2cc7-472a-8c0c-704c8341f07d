import { v2 as cloudinary } from 'cloudinary';
import { logger } from '@/utils/logger';

export interface CloudinaryConfig {
  cloudName: string;
  apiKey: string;
  apiSecret: string;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  publicId?: string;
  error?: string;
}

export class CloudinaryService {
  private config: CloudinaryConfig;

  constructor() {
    this.config = {
      cloudName: process.env.CLOUDINARY_CLOUD_NAME || '',
      apiKey: process.env.CLOUDINARY_API_KEY || '',
      apiSecret: process.env.CLOUDINARY_API_SECRET || '',
    };

    // Configure Cloudinary
    if (this.config.cloudName && this.config.apiKey && this.config.apiSecret) {
      cloudinary.config({
        cloud_name: this.config.cloudName,
        api_key: this.config.apiKey,
        api_secret: this.config.apiSecret,
      });
    }
  }

  async uploadImage(
    buffer: Buffer,
    options: {
      folder?: string;
      publicId?: string;
      transformation?: any;
    } = {}
  ): Promise<UploadResult> {
    try {
      if (!this.config.cloudName || !this.config.apiKey || !this.config.apiSecret) {
        logger.warn('Cloudinary not configured, image upload skipped');
        return {
          success: false,
          error: 'Image upload service not configured',
        };
      }

      const uploadOptions = {
        folder: options.folder || 'pedi/clothing',
        public_id: options.publicId,
        transformation: options.transformation || [
          { width: 800, height: 800, crop: 'limit', quality: 'auto' },
          { fetch_format: 'auto' }
        ],
        resource_type: 'image' as const,
      };

      const result = await new Promise((resolve, reject) => {
        cloudinary.uploader.upload_stream(
          uploadOptions,
          (error, result) => {
            if (error) reject(error);
            else resolve(result);
          }
        ).end(buffer);
      }) as any;

      logger.info('Image uploaded successfully to Cloudinary', {
        publicId: result.public_id,
        url: result.secure_url,
      });

      return {
        success: true,
        url: result.secure_url,
        publicId: result.public_id,
      };

    } catch (error) {
      logger.error('Failed to upload image to Cloudinary:', error);
      return {
        success: false,
        error: 'Failed to upload image',
      };
    }
  }

  async uploadMultipleImages(
    buffers: Buffer[],
    options: {
      folder?: string;
      transformation?: any;
    } = {}
  ): Promise<UploadResult[]> {
    const uploadPromises = buffers.map((buffer, index) =>
      this.uploadImage(buffer, {
        ...options,
        publicId: `${Date.now()}_${index}`,
      })
    );

    return Promise.all(uploadPromises);
  }

  async deleteImage(publicId: string): Promise<boolean> {
    try {
      if (!this.config.cloudName || !this.config.apiKey || !this.config.apiSecret) {
        logger.warn('Cloudinary not configured, image deletion skipped');
        return false;
      }

      const result = await cloudinary.uploader.destroy(publicId);
      
      if (result.result === 'ok') {
        logger.info('Image deleted successfully from Cloudinary', { publicId });
        return true;
      }

      return false;

    } catch (error) {
      logger.error('Failed to delete image from Cloudinary:', error);
      return false;
    }
  }

  async deleteMultipleImages(publicIds: string[]): Promise<boolean[]> {
    const deletePromises = publicIds.map(publicId => this.deleteImage(publicId));
    return Promise.all(deletePromises);
  }

  generateTransformationUrl(
    publicId: string,
    transformations: any[]
  ): string {
    if (!this.config.cloudName) {
      return '';
    }

    return cloudinary.url(publicId, {
      transformation: transformations,
      secure: true,
    });
  }

  // Generate different sizes for clothing images
  generateClothingImageUrls(publicId: string): {
    thumbnail: string;
    medium: string;
    large: string;
    original: string;
  } {
    const baseUrl = `https://res.cloudinary.com/${this.config.cloudName}/image/upload`;
    
    return {
      thumbnail: `${baseUrl}/w_150,h_150,c_fill,q_auto,f_auto/${publicId}`,
      medium: `${baseUrl}/w_400,h_400,c_limit,q_auto,f_auto/${publicId}`,
      large: `${baseUrl}/w_800,h_800,c_limit,q_auto,f_auto/${publicId}`,
      original: `${baseUrl}/q_auto,f_auto/${publicId}`,
    };
  }
}

export const cloudinaryService = new CloudinaryService();
