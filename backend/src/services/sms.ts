import axios from 'axios';
import { logger } from '@/utils/logger';

export interface SMSConfig {
  username: string;
  apiKey: string;
  senderId: string;
}

export interface SMSMessage {
  to: string;
  message: string;
}

export interface SMSResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export class SMSService {
  private config: SMSConfig;
  private baseUrl = 'https://api.africastalking.com/version1';

  constructor() {
    this.config = {
      username: process.env.AFRICAS_TALKING_USERNAME || '',
      apiKey: process.env.AFRICAS_TALKING_API_KEY || '',
      senderId: process.env.AFRICAS_TALKING_SENDER_ID || 'PEDI',
    };
  }

  async sendSMS(smsData: SMSMessage): Promise<SMSResponse> {
    try {
      if (!this.config.username || !this.config.apiKey) {
        logger.warn('Africa\'s Talking credentials not configured, SMS not sent');
        return { success: false, error: 'SMS service not configured' };
      }

      const requestBody = new URLSearchParams({
        username: this.config.username,
        to: smsData.to,
        message: smsData.message,
        from: this.config.senderId,
      });

      const response = await axios.post(
        `${this.baseUrl}/messaging`,
        requestBody,
        {
          headers: {
            'apiKey': this.config.apiKey,
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
          },
        }
      );

      const result = response.data.SMSMessageData;
      
      if (result.Recipients && result.Recipients.length > 0) {
        const recipient = result.Recipients[0];
        
        if (recipient.statusCode === 101) {
          logger.info('SMS sent successfully', {
            to: smsData.to,
            messageId: recipient.messageId,
          });
          
          return {
            success: true,
            messageId: recipient.messageId,
          };
        } else {
          logger.error('SMS failed to send', {
            to: smsData.to,
            statusCode: recipient.statusCode,
            status: recipient.status,
          });
          
          return {
            success: false,
            error: recipient.status,
          };
        }
      }

      return {
        success: false,
        error: 'Unknown error occurred',
      };

    } catch (error) {
      logger.error('Failed to send SMS:', error);
      return {
        success: false,
        error: 'Failed to send SMS',
      };
    }
  }

  async sendOTP(phoneNumber: string, otp: string): Promise<SMSResponse> {
    const message = `Your Pedi verification code is: ${otp}. This code will expire in 10 minutes. Do not share this code with anyone.`;
    
    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  async sendTransactionNotification(phoneNumber: string, transactionType: string, details: string): Promise<SMSResponse> {
    const message = `Pedi: Your ${transactionType} transaction has been updated. ${details}. Check the app for more details.`;
    
    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  async sendWelcomeMessage(phoneNumber: string, firstName: string): Promise<SMSResponse> {
    const message = `Welcome to Pedi, ${firstName}! 🌍 Start your sustainable fashion journey by listing your first item or browsing available clothes. You've earned 100 Pedi tokens to get started!`;
    
    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  async sendTokenEarnedNotification(phoneNumber: string, amount: number, reason: string): Promise<SMSResponse> {
    const message = `Pedi: You've earned ${amount} tokens for ${reason}! Your sustainable actions are making a difference. 🌱`;
    
    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }
}

export const smsService = new SMSService();
