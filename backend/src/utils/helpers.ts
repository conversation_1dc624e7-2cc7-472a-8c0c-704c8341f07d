import crypto from 'crypto';
import { Types } from 'mongoose';

/**
 * Generate a random OTP
 */
export const generateOTP = (length: number = 6): string => {
  const digits = '0123456789';
  let otp = '';
  
  for (let i = 0; i < length; i++) {
    otp += digits[Math.floor(Math.random() * digits.length)];
  }
  
  return otp;
};

/**
 * Generate a random token
 */
export const generateToken = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Format phone number to international format
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  // Remove any spaces, dashes, or other non-numeric characters except +
  const cleaned = phoneNumber.replace(/[^\d+]/g, '');
  
  // If it starts with 0, replace with +254
  if (cleaned.startsWith('0')) {
    return '+254' + cleaned.substring(1);
  }
  
  // If it starts with 254, add +
  if (cleaned.startsWith('254')) {
    return '+' + cleaned;
  }
  
  // If it already starts with +254, return as is
  if (cleaned.startsWith('+254')) {
    return cleaned;
  }
  
  // Default case - assume it's a Kenyan number
  return '+254' + cleaned;
};

/**
 * Calculate distance between two coordinates (Haversine formula)
 */
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return distance;
};

/**
 * Convert degrees to radians
 */
const toRadians = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

/**
 * Calculate sustainability score based on various factors
 */
export const calculateSustainabilityScore = (factors: {
  donationCount: number;
  swapCount: number;
  listingCount: number;
  accountAge: number; // in days
  qualityRating: number;
}): number => {
  let score = 0;
  
  // Base score for donations (5 points each, max 200)
  score += Math.min(factors.donationCount * 5, 200);
  
  // Score for swaps (3 points each, max 150)
  score += Math.min(factors.swapCount * 3, 150);
  
  // Score for listings (1 point each, max 50)
  score += Math.min(factors.listingCount * 1, 50);
  
  // Account age bonus (1 point per week, max 100)
  const weeksOld = Math.floor(factors.accountAge / 7);
  score += Math.min(weeksOld, 100);
  
  // Quality rating bonus (rating * 20, max 100)
  score += Math.min(factors.qualityRating * 20, 100);
  
  return Math.min(score, 1000); // Cap at 1000
};

/**
 * Generate a unique reference number for transactions
 */
export const generateTransactionReference = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `PEDI-${timestamp}-${random}`.toUpperCase();
};

/**
 * Validate if a string is a valid MongoDB ObjectId
 */
export const isValidObjectId = (id: string): boolean => {
  return Types.ObjectId.isValid(id);
};

/**
 * Sanitize user input to prevent XSS
 */
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .trim();
};

/**
 * Calculate token rewards for different actions
 */
export const calculateTokenReward = (action: string, metadata?: any): number => {
  const rewards = {
    'listing': 10,
    'donation': 50,
    'swap_completed': 25,
    'review_given': 5,
    'profile_completed': 20,
    'referral': 100,
    'daily_login': 2,
    'first_listing': 50,
    'first_swap': 75,
    'first_donation': 100,
  };
  
  return rewards[action as keyof typeof rewards] || 0;
};

/**
 * Format currency for display
 */
export const formatCurrency = (amount: number, currency: string = 'KES'): string => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Get time ago string
 */
export const getTimeAgo = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'just now';
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }
  
  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
  }
  
  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
};

/**
 * Generate a slug from a string
 */
export const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
};

/**
 * Check if user is within delivery radius
 */
export const isWithinDeliveryRadius = (
  userCoords: { latitude: number; longitude: number },
  itemCoords: { latitude: number; longitude: number },
  radiusKm: number = 50
): boolean => {
  const distance = calculateDistance(
    userCoords.latitude,
    userCoords.longitude,
    itemCoords.latitude,
    itemCoords.longitude
  );
  
  return distance <= radiusKm;
};
