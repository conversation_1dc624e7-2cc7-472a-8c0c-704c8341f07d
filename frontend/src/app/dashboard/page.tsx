'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Coins, 
  Shirt, 
  Heart, 
  Recycle, 
  TrendingUp,
  MapPin,
  Phone,
  Mail,
  LogOut
} from 'lucide-react';

interface User {
  id: string;
  phoneNumber: string;
  email?: string;
  firstName: string;
  lastName: string;
  fullName: string;
  profilePicture?: string;
  location: {
    county: string;
    town: string;
  };
  isVerified: boolean;
  role: string;
  pediTokens: number;
  rating: {
    average: number;
    count: number;
  };
  sustainabilityScore: number;
  totalDonations: number;
  totalSwaps: number;
  joinedAt: string;
  lastActive: string;
}

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/auth');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/auth');
    } finally {
      setLoading(false);
    }
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/auth');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold text-green-600">Pedi</h1>
              <Badge variant="secondary">Dashboard</Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{user.fullName}</p>
                <p className="text-xs text-gray-500">{user.pediTokens} Pedi Tokens</p>
              </div>
              
              {user.profilePicture ? (
                <img 
                  src={user.profilePicture} 
                  alt="Profile" 
                  className="h-8 w-8 rounded-full"
                />
              ) : (
                <div className="h-8 w-8 rounded-full bg-green-600 flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
              )}
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleLogout}
                className="text-red-600 hover:text-red-700"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user.firstName}! 👋
          </h2>
          <p className="text-gray-600">
            Ready to continue your sustainable fashion journey?
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pedi Tokens</CardTitle>
              <Coins className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{user.pediTokens}</div>
              <p className="text-xs text-muted-foreground">
                Your sustainable currency
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Swaps</CardTitle>
              <Recycle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{user.totalSwaps}</div>
              <p className="text-xs text-muted-foreground">
                Clothing exchanges completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Donations</CardTitle>
              <Heart className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{user.totalDonations}</div>
              <p className="text-xs text-muted-foreground">
                Items donated to charity
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Sustainability Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{user.sustainabilityScore}</div>
              <p className="text-xs text-muted-foreground">
                Your eco-impact rating
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Profile and Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Profile Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Your account details and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-gray-400" />
                <span className="text-sm">{user.phoneNumber}</span>
                {user.isVerified && (
                  <Badge variant="secondary" className="text-xs">Verified</Badge>
                )}
              </div>
              
              {user.email && (
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{user.email}</span>
                </div>
              )}
              
              <div className="flex items-center gap-3">
                <MapPin className="h-4 w-4 text-gray-400" />
                <span className="text-sm">{user.location.town}, {user.location.county}</span>
              </div>
              
              <div className="pt-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>User Rating</span>
                  <span>{user.rating.average}/5.0 ({user.rating.count} reviews)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-yellow-400 h-2 rounded-full" 
                    style={{ width: `${(user.rating.average / 5) * 100}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shirt className="h-5 w-5" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Get started with your sustainable fashion journey
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full justify-start" variant="outline">
                <Shirt className="h-4 w-4 mr-2" />
                List New Clothing Item
              </Button>
              
              <Button className="w-full justify-start" variant="outline">
                <Recycle className="h-4 w-4 mr-2" />
                Browse Available Swaps
              </Button>
              
              <Button className="w-full justify-start" variant="outline">
                <Heart className="h-4 w-4 mr-2" />
                Donate to Charity
              </Button>
              
              <Button className="w-full justify-start" variant="outline">
                <Coins className="h-4 w-4 mr-2" />
                View Token History
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Coming Soon Notice */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="text-center">🚧 More Features Coming Soon!</CardTitle>
            <CardDescription className="text-center">
              We're working hard to bring you the full Pedi experience. Stay tuned for:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="p-4 bg-gray-50 rounded-lg">
                <Shirt className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <h3 className="font-medium">Clothing Marketplace</h3>
                <p className="text-sm text-gray-600">Browse and swap clothes</p>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <Coins className="h-8 w-8 mx-auto mb-2 text-yellow-600" />
                <h3 className="font-medium">Token Economy</h3>
                <p className="text-sm text-gray-600">Earn and spend Pedi tokens</p>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <Heart className="h-8 w-8 mx-auto mb-2 text-red-600" />
                <h3 className="font-medium">Charity Integration</h3>
                <p className="text-sm text-gray-600">Donate to local organizations</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
