/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m20 19.5-5.5 1.2", key: "1aenhr" }],
  ["path", { d: "M14.5 4v11.22a1 1 0 0 0 1.242.97L20 15.2", key: "2rtezt" }],
  ["path", { d: "m2.978 19.351 5.549-1.363A2 2 0 0 0 10 16V2", key: "1kbm92" }],
  ["path", { d: "M20 10 4 13.5", key: "8nums9" }]
];
const SaudiRiyal = createLucideIcon("saudi-riyal", __iconNode);

export { __iconNode, SaudiRiyal as default };
//# sourceMappingURL=saudi-riyal.js.map
