/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["circle", { cx: "10", cy: "7", r: "4", key: "e45bow" }],
  ["path", { d: "M10.3 15H7a4 4 0 0 0-4 4v2", key: "3bnktk" }],
  ["path", { d: "M15 15.5V14a2 2 0 0 1 4 0v1.5", key: "12ym5i" }],
  ["rect", { width: "8", height: "5", x: "13", y: "16", rx: ".899", key: "4p176n" }]
];
const UserLock = createLucideIcon("user-lock", __iconNode);

export { __iconNode, UserLock as default };
//# sourceMappingURL=user-lock.js.map
